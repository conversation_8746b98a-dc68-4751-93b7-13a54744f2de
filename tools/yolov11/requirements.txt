# YOLOv11门窗检测算法依赖包
# =====================================

# 核心深度学习框架
torch>=2.0.0
torchvision>=0.15.0

# YOLOv11模型库
ultralytics>=8.0.0

# 图像处理
opencv-python>=4.8.0
Pillow>=9.0.0
numpy>=1.21.0

# 数据处理和工具
pathlib2>=2.3.0  # Python < 3.4 兼容性
argparse  # 通常内置，但确保可用

# 日志和调试
logging  # 内置模块

# 可选：性能优化
# opencv-contrib-python>=4.8.0  # 额外的OpenCV功能
# torch-audio>=2.0.0  # 如果需要音频处理

# 可选：GPU加速 (根据系统选择安装)
# torch+cu118  # CUDA 11.8版本
# torch+cu121  # CUDA 12.1版本

# 开发和测试工具 (可选)
# pytest>=7.0.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
