#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11门窗检测算法安装和设置脚本
==================================

本脚本用于自动安装依赖包和设置环境，确保门窗检测算法能够正常运行。

功能：
1. 检查Python版本
2. 安装必要的依赖包
3. 验证模型文件
4. 创建必要的目录结构
5. 运行基本功能测试

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import sys
import subprocess
import urllib.request
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    if not requirements_file.exists():
        print(f"❌ 依赖文件不存在: {requirements_file}")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False


def check_model_file():
    """检查模型文件"""
    print("\n🤖 检查模型文件...")
    
    model_path = Path("checkpopints/yolo11x.pt")
    if model_path.exists():
        size_mb = model_path.stat().st_size / (1024 * 1024)
        print(f"✅ 模型文件存在: {model_path} ({size_mb:.1f} MB)")
        return True
    
    print(f"⚠️  模型文件不存在: {model_path}")
    print("   请从以下地址下载YOLOv11模型:")
    print("   https://github.com/ultralytics/assets/releases/download/v0.0.0/yolo11x.pt")
    
    # 询问是否自动下载
    try:
        response = input("是否自动下载模型文件? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            return download_model()
    except KeyboardInterrupt:
        print("\n用户取消操作")
    
    return False


def download_model():
    """下载模型文件"""
    print("📥 正在下载YOLOv11模型...")
    
    model_url = "https://github.com/ultralytics/assets/releases/download/v0.0.0/yolo11x.pt"
    model_path = Path("checkpopints/yolo11x.pt")
    
    try:
        # 创建目录
        model_path.parent.mkdir(exist_ok=True)
        
        # 下载文件
        urllib.request.urlretrieve(model_url, model_path)
        
        size_mb = model_path.stat().st_size / (1024 * 1024)
        print(f"✅ 模型下载完成: {model_path} ({size_mb:.1f} MB)")
        return True
        
    except Exception as e:
        print(f"❌ 模型下载失败: {e}")
        print("   请手动下载模型文件并放置到正确位置")
        return False


def create_directories():
    """创建必要的目录结构"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "work_dirs/door_windows_imgs",
        "work_dirs/yolo_v11_results",
        "work_dirs/yolo_v11_results/visualizations",
        "tools/debug",
        "checkpopints"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory}")
    
    print("✅ 目录结构创建完成")
    return True


def run_basic_test():
    """运行基本功能测试"""
    print("\n🧪 运行基本功能测试...")
    
    try:
        # 测试导入
        import torch
        import cv2
        import numpy as np
        from PIL import Image
        from ultralytics import YOLO
        
        print("✅ 核心库导入成功")
        
        # 测试CUDA可用性
        if torch.cuda.is_available():
            print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        else:
            print("⚠️  CUDA不可用，将使用CPU进行推理")
        
        return True
        
    except ImportError as e:
        print(f"❌ 库导入失败: {e}")
        return False


def main():
    """主安装函数"""
    print("🚀 YOLOv11门窗检测算法安装程序")
    print("=" * 50)
    
    # 执行安装步骤
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_dependencies),
        ("检查模型文件", check_model_file),
        ("创建目录结构", create_directories),
        ("运行基本测试", run_basic_test)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name} 未完全成功")
        except Exception as e:
            print(f"❌ {step_name} 执行异常: {e}")
    
    # 显示安装结果
    print("\n" + "=" * 50)
    print("📊 安装结果总结")
    print("=" * 50)
    
    if success_count == len(steps):
        print("🎉 安装完成! 所有组件都已就绪")
        print("\n📖 使用说明:")
        print("1. 将待检测图像放入 work_dirs/door_windows_imgs/ 目录")
        print("2. 运行检测: python tools/yolov11/door_windows_detection.py")
        print("3. 查看结果: work_dirs/yolo_v11_results/")
        print("\n🔧 调试测试: python tools/debug/test_yolo_detection.py")
    else:
        print(f"⚠️  安装部分完成 ({success_count}/{len(steps)})")
        print("请检查上述错误信息并手动解决相关问题")
    
    print("\n📚 详细使用说明请参考: tools/yolov11/README.md")


if __name__ == '__main__':
    main()
