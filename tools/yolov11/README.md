# YOLOv11门窗检测算法

## 概述

本项目实现了基于YOLOv11的门窗检测算法，支持批量处理图像文件夹中的所有图像，并输出检测结果和可视化图像。

## 功能特性

- ✅ 基于YOLOv11预训练模型的门窗检测
- ✅ 批量处理图像文件夹
- ✅ 输出边界框坐标和置信度
- ✅ 生成可视化检测结果
- ✅ 支持多种图像格式 (jpg, png, bmp, tiff)
- ✅ 完整的中文注释和文档
- ✅ 错误处理和日志记录
- ✅ 性能统计和摘要报告

## 目录结构

```
tools/yolov11/
├── door_windows_detection.py  # 主检测脚本
├── README.md                  # 使用说明
└── requirements.txt           # 依赖包列表

tools/debug/
├── test_yolo_detection.py     # 调试测试脚本
└── test_output/               # 测试输出目录

work_dirs/
├── door_windows_imgs/         # 输入图像目录
└── yolo_v11_results/          # 输出结果目录
    ├── visualizations/        # 可视化结果图像
    ├── detection_results.json # 检测结果JSON文件
    └── detection_summary.txt  # 检测摘要报告

checkpopints/
└── yolo11x.pt                 # YOLOv11预训练模型
```

## 安装依赖

1. 安装Python依赖包：
```bash
pip install -r tools/yolov11/requirements.txt
```

2. 确保已下载YOLOv11模型文件到 `checkpopints/yolo11x.pt`

## 使用方法

### 基本使用

使用默认参数运行检测：
```bash
python tools/yolov11/door_windows_detection.py
```

### 自定义参数

```bash
python tools/yolov11/door_windows_detection.py \
    --input work_dirs/door_windows_imgs \
    --output work_dirs/yolo_v11_results \
    --model checkpopints/yolo11x.pt \
    --conf 0.5 \
    --verbose
```

### 参数说明

- `--input, -i`: 输入图像文件夹路径 (默认: work_dirs/door_windows_imgs)
- `--output, -o`: 输出结果文件夹路径 (默认: work_dirs/yolo_v11_results)
- `--model, -m`: YOLOv11模型文件路径 (默认: checkpopints/yolo11x.pt)
- `--conf, -c`: 置信度阈值 (默认: 0.5, 范围: 0.0-1.0)
- `--verbose, -v`: 显示详细日志信息

## 输出结果

### 1. 可视化图像
- 位置: `{output_dir}/visualizations/`
- 格式: 原图像名前加 `detected_` 前缀
- 内容: 带有边界框和标签的检测结果图像

### 2. JSON检测结果
- 文件: `{output_dir}/detection_results.json`
- 内容: 完整的检测结果数据，包括：
  - 每张图像的检测结果
  - 边界框坐标
  - 置信度分数
  - 类别信息
  - 统计信息

### 3. 摘要报告
- 文件: `{output_dir}/detection_summary.txt`
- 内容: 人类可读的检测结果摘要

## 调试和测试

运行调试脚本进行功能测试：
```bash
python tools/debug/test_yolo_detection.py
```

调试脚本包含以下测试：
1. 模型加载测试
2. 单张图像检测测试
3. 小批量处理测试
4. 性能基准测试

## 配置说明

### 门窗类别配置

在 `DoorWindowDetector` 类中，可以配置检测的目标类别：

```python
# 方法1: 基于COCO类别ID
self.door_window_classes = {
    'door': [62],  # door-stuff类别
    'window': [63, 64, 65],  # window相关类别
}

# 方法2: 基于类别名称关键词
door_window_keywords = ['door', 'window', '门', '窗', 'gate', 'entrance']
```

### 置信度阈值

- 默认值: 0.5
- 建议范围: 0.3-0.7
- 较低值: 检测更多目标，但可能包含误检
- 较高值: 检测更准确，但可能遗漏目标

## 性能优化建议

1. **GPU加速**: 确保安装了CUDA版本的PyTorch以使用GPU加速
2. **批量大小**: 对于大量图像，可以考虑批量处理以提高效率
3. **图像尺寸**: 较大的图像会增加处理时间，可以考虑预处理调整尺寸
4. **模型选择**: 
   - `yolo11n.pt`: 最快，精度较低
   - `yolo11x.pt`: 最慢，精度最高

## 常见问题

### Q: 模型加载失败
A: 检查模型文件路径是否正确，确保已下载完整的模型文件

### Q: 检测不到门窗
A: 尝试降低置信度阈值，或检查图像中是否确实包含门窗

### Q: 内存不足
A: 尝试使用较小的模型 (yolo11n.pt) 或减少批量处理的图像数量

### Q: 检测结果不准确
A: 考虑使用专门训练的门窗检测模型，或调整置信度阈值

## 技术支持

如有问题或建议，请查看日志文件 `door_windows_detection.log` 获取详细错误信息。

## 更新日志

- v1.0.0 (2025-09-01): 初始版本，支持基本的门窗检测功能
