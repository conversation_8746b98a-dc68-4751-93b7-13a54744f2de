#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11门窗检测算法
===================

本脚本实现基于YOLOv11的门窗检测功能，支持批量处理图像文件夹中的所有图像。

主要功能：
1. 加载预训练的YOLOv11模型
2. 批量处理输入文件夹中的图像
3. 检测门窗目标并输出边界框坐标
4. 生成可视化结果图像
5. 保存检测结果为JSON格式

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import json
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import time

import cv2
import numpy as np
from PIL import Image
import torch

from ultralytics import YOLO


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('door_windows_detection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DoorWindowDetector:
    """
    门窗检测器类

    该类封装了YOLOv11模型的加载、推理和结果处理功能，
    专门用于检测图像中的门窗目标。
    """

    def __init__(self, model_path: str, confidence_threshold: float = 0.5):
        """
        初始化门窗检测器

        Args:
            model_path (str): YOLOv11模型文件路径
            confidence_threshold (float): 置信度阈值，默认0.5
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.model = None

        # COCO数据集中门窗相关的类别ID
        # 0: person, 1: bicycle, 2: car, ..., 62: door-stuff, 63: window-blind, ...
        # 这里我们关注可能包含门窗的类别
        self.door_window_classes = {
            'door': [62],  # door-stuff类别
            'window': [63, 64, 65],  # window相关类别
            # 注意：标准COCO数据集可能没有专门的门窗类别
            # 这里的类别ID需要根据实际使用的模型进行调整
        }

        # 如果使用自定义训练的模型，可以直接指定类别名称
        self.target_classes = ['door', 'window']

        self._load_model()

    def _load_model(self) -> None:
        """
        加载YOLOv11模型

        Raises:
            FileNotFoundError: 当模型文件不存在时抛出
            Exception: 当模型加载失败时抛出
        """
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            logger.info(f"正在加载YOLOv11模型: {self.model_path}")
            self.model = YOLO(self.model_path)
            logger.info("模型加载成功")

            # 打印模型信息
            if hasattr(self.model, 'names'):
                logger.info(f"模型支持的类别: {self.model.names}")

        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def detect_image(self, image_path: str) -> Tuple[List[Dict], np.ndarray]:
        """
        对单张图像进行门窗检测

        Args:
            image_path (str): 图像文件路径

        Returns:
            Tuple[List[Dict], np.ndarray]: 检测结果列表和可视化图像

        Raises:
            FileNotFoundError: 当图像文件不存在时抛出
            Exception: 当检测过程出错时抛出
        """
        try:
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")

            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像文件: {image_path}")

            # 进行推理
            results = self.model(image, conf=self.confidence_threshold)

            # 解析检测结果
            detections = []
            annotated_image = image.copy()

            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())

                        # 获取类别名称
                        class_name = self.model.names[class_id] if hasattr(self.model, 'names') else str(class_id)

                        # 检查是否为目标类别（门窗）
                        if self._is_door_window_class(class_name, class_id):
                            detection = {
                                'class_name': class_name,
                                'class_id': class_id,
                                'confidence': float(confidence),
                                'bbox': {
                                    'x1': float(x1),
                                    'y1': float(y1),
                                    'x2': float(x2),
                                    'y2': float(y2),
                                    'width': float(x2 - x1),
                                    'height': float(y2 - y1)
                                }
                            }
                            detections.append(detection)

                            # 在图像上绘制边界框
                            annotated_image = self._draw_bbox(
                                annotated_image, x1, y1, x2, y2,
                                class_name, confidence
                            )

            return detections, annotated_image

        except Exception as e:
            logger.error(f"检测图像时出错 {image_path}: {str(e)}")
            raise

    def _is_door_window_class(self, class_name: str, class_id: int) -> bool:
        """
        判断检测到的类别是否为门窗相关类别

        Args:
            class_name (str): 类别名称
            class_id (int): 类别ID

        Returns:
            bool: 如果是门窗类别返回True，否则返回False
        """
        # 方法1: 根据类别名称判断
        door_window_keywords = ['door', 'window', '门', '窗', 'gate', 'entrance']
        for keyword in door_window_keywords:
            if keyword.lower() in class_name.lower():
                return True

        # 方法2: 根据类别ID判断（适用于自定义模型）
        all_target_ids = []
        for ids in self.door_window_classes.values():
            all_target_ids.extend(ids)

        return class_id in all_target_ids

    def _draw_bbox(self, image: np.ndarray, x1: float, y1: float, x2: float, y2: float,
                   class_name: str, confidence: float) -> np.ndarray:
        """
        在图像上绘制边界框和标签

        Args:
            image (np.ndarray): 输入图像
            x1, y1, x2, y2 (float): 边界框坐标
            class_name (str): 类别名称
            confidence (float): 置信度

        Returns:
            np.ndarray: 绘制了边界框的图像
        """
        # 设置颜色（BGR格式）
        color = (0, 255, 0) if 'door' in class_name.lower() else (255, 0, 0)  # 门用绿色，窗用蓝色
        thickness = 2

        # 绘制边界框
        cv2.rectangle(image, (int(x1), int(y1)), (int(x2), int(y2)), color, thickness)

        # 准备标签文本
        label = f"{class_name}: {confidence:.2f}"

        # 计算文本大小
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        text_thickness = 1
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, text_thickness)

        # 绘制文本背景
        cv2.rectangle(image,
                     (int(x1), int(y1) - text_height - baseline - 5),
                     (int(x1) + text_width, int(y1)),
                     color, -1)

        # 绘制文本
        cv2.putText(image, label,
                   (int(x1), int(y1) - baseline - 2),
                   font, font_scale, (255, 255, 255), text_thickness)

        return image

    def batch_detect(self, input_dir: str, output_dir: str) -> Dict:
        """
        批量检测文件夹中的所有图像

        Args:
            input_dir (str): 输入图像文件夹路径
            output_dir (str): 输出结果文件夹路径

        Returns:
            Dict: 批量检测的统计结果
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            os.makedirs(os.path.join(output_dir, 'visualizations'), exist_ok=True)

            # 支持的图像格式
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

            # 获取所有图像文件
            image_files = []
            for file_path in Path(input_dir).iterdir():
                if file_path.suffix.lower() in supported_formats:
                    image_files.append(file_path)

            if not image_files:
                logger.warning(f"在目录 {input_dir} 中未找到支持的图像文件")
                return {'total_images': 0, 'processed_images': 0, 'total_detections': 0}

            logger.info(f"找到 {len(image_files)} 张图像，开始批量检测...")

            # 批量检测结果
            batch_results = {
                'detection_results': {},
                'statistics': {
                    'total_images': len(image_files),
                    'processed_images': 0,
                    'failed_images': 0,
                    'total_detections': 0,
                    'door_detections': 0,
                    'window_detections': 0
                },
                'processing_time': 0
            }

            start_time = time.time()

            # 逐个处理图像
            for i, image_file in enumerate(image_files, 1):
                try:
                    logger.info(f"处理图像 {i}/{len(image_files)}: {image_file.name}")

                    # 检测当前图像
                    detections, annotated_image = self.detect_image(str(image_file))

                    # 保存检测结果
                    image_result = {
                        'image_path': str(image_file),
                        'image_name': image_file.name,
                        'detections': detections,
                        'detection_count': len(detections)
                    }

                    batch_results['detection_results'][image_file.name] = image_result

                    # 保存可视化结果
                    vis_output_path = os.path.join(output_dir, 'visualizations', f"detected_{image_file.name}")
                    cv2.imwrite(vis_output_path, annotated_image)

                    # 更新统计信息
                    batch_results['statistics']['processed_images'] += 1
                    batch_results['statistics']['total_detections'] += len(detections)

                    # 统计门窗数量
                    for detection in detections:
                        if 'door' in detection['class_name'].lower():
                            batch_results['statistics']['door_detections'] += 1
                        elif 'window' in detection['class_name'].lower():
                            batch_results['statistics']['window_detections'] += 1

                    logger.info(f"图像 {image_file.name} 检测完成，发现 {len(detections)} 个目标")

                except Exception as e:
                    logger.error(f"处理图像 {image_file.name} 时出错: {str(e)}")
                    batch_results['statistics']['failed_images'] += 1
                    continue

            # 计算处理时间
            batch_results['processing_time'] = time.time() - start_time

            # 保存批量检测结果为JSON文件
            results_json_path = os.path.join(output_dir, 'detection_results.json')
            with open(results_json_path, 'w', encoding='utf-8') as f:
                json.dump(batch_results, f, ensure_ascii=False, indent=2)

            # 打印统计信息
            stats = batch_results['statistics']
            logger.info("=" * 50)
            logger.info("批量检测完成！")
            logger.info(f"总图像数: {stats['total_images']}")
            logger.info(f"成功处理: {stats['processed_images']}")
            logger.info(f"处理失败: {stats['failed_images']}")
            logger.info(f"总检测数: {stats['total_detections']}")
            logger.info(f"门检测数: {stats['door_detections']}")
            logger.info(f"窗检测数: {stats['window_detections']}")
            logger.info(f"处理时间: {batch_results['processing_time']:.2f} 秒")
            logger.info(f"结果已保存到: {output_dir}")
            logger.info("=" * 50)

            return batch_results

        except Exception as e:
            logger.error(f"批量检测过程中出错: {str(e)}")
            raise


def create_summary_report(results_json_path: str, output_dir: str) -> None:
    """
    创建检测结果摘要报告

    Args:
        results_json_path (str): 检测结果JSON文件路径
        output_dir (str): 输出目录
    """
    try:
        with open(results_json_path, 'r', encoding='utf-8') as f:
            results = json.load(f)

        # 创建摘要报告
        report_lines = []
        report_lines.append("门窗检测结果摘要报告")
        report_lines.append("=" * 50)
        report_lines.append("")

        # 统计信息
        stats = results['statistics']
        report_lines.append("检测统计:")
        report_lines.append(f"  总图像数: {stats['total_images']}")
        report_lines.append(f"  成功处理: {stats['processed_images']}")
        report_lines.append(f"  处理失败: {stats['failed_images']}")
        report_lines.append(f"  总检测数: {stats['total_detections']}")
        report_lines.append(f"  门检测数: {stats['door_detections']}")
        report_lines.append(f"  窗检测数: {stats['window_detections']}")
        report_lines.append(f"  处理时间: {results['processing_time']:.2f} 秒")
        report_lines.append("")

        # 详细检测结果
        report_lines.append("详细检测结果:")
        report_lines.append("-" * 30)

        for image_name, image_result in results['detection_results'].items():
            report_lines.append(f"\n图像: {image_name}")
            report_lines.append(f"  检测数量: {image_result['detection_count']}")

            if image_result['detections']:
                for i, detection in enumerate(image_result['detections'], 1):
                    bbox = detection['bbox']
                    report_lines.append(f"  检测 {i}:")
                    report_lines.append(f"    类别: {detection['class_name']}")
                    report_lines.append(f"    置信度: {detection['confidence']:.3f}")
                    report_lines.append(f"    边界框: ({bbox['x1']:.1f}, {bbox['y1']:.1f}, {bbox['x2']:.1f}, {bbox['y2']:.1f})")
                    report_lines.append(f"    尺寸: {bbox['width']:.1f} x {bbox['height']:.1f}")
            else:
                report_lines.append("  未检测到门窗目标")

        # 保存报告
        report_path = os.path.join(output_dir, 'detection_summary.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        logger.info(f"摘要报告已保存到: {report_path}")

    except Exception as e:
        logger.error(f"创建摘要报告时出错: {str(e)}")


def main():
    """
    主函数 - 解析命令行参数并执行门窗检测
    """
    parser = argparse.ArgumentParser(
        description='YOLOv11门窗检测算法',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python door_windows_detection.py --input work_dirs/door_windows_imgs --output work_dirs/yolo_v11_results
  python door_windows_detection.py --input /path/to/images --output /path/to/results --model checkpopints/yolo11x.pt --conf 0.6
        """
    )

    parser.add_argument(
        '--input', '-i',
        type=str,
        default='work_dirs/door_windows_imgs',
        help='输入图像文件夹路径 (默认: work_dirs/door_windows_imgs)'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        default='work_dirs/yolo_v11_results',
        help='输出结果文件夹路径 (默认: work_dirs/yolo_v11_results)'
    )

    parser.add_argument(
        '--model', '-m',
        type=str,
        default='checkpopints/yolo11x.pt',
        help='YOLOv11模型文件路径 (默认: checkpopints/yolo11x.pt)'
    )

    parser.add_argument(
        '--conf', '-c',
        type=float,
        default=0.5,
        help='置信度阈值 (默认: 0.5)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志信息'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 验证输入参数
        if not os.path.exists(args.input):
            logger.error(f"输入目录不存在: {args.input}")
            return

        if not os.path.exists(args.model):
            logger.error(f"模型文件不存在: {args.model}")
            return

        if not (0.0 <= args.conf <= 1.0):
            logger.error(f"置信度阈值必须在0.0到1.0之间: {args.conf}")
            return

        # 创建检测器实例
        logger.info("初始化门窗检测器...")
        detector = DoorWindowDetector(
            model_path=args.model,
            confidence_threshold=args.conf
        )

        # 执行批量检测
        logger.info("开始批量检测...")
        batch_results = detector.batch_detect(args.input, args.output)

        # 创建摘要报告
        results_json_path = os.path.join(args.output, 'detection_results.json')
        create_summary_report(results_json_path, args.output)

        # 显示简要统计信息
        stats = batch_results['statistics']
        print(f"\n检测完成! 处理了 {stats['processed_images']}/{stats['total_images']} 张图像")
        print(f"共检测到 {stats['total_detections']} 个目标 (门: {stats['door_detections']}, 窗: {stats['window_detections']})")

        logger.info("门窗检测任务完成！")

    except KeyboardInterrupt:
        logger.info("用户中断了检测过程")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise


if __name__ == '__main__':
    main()

