#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11门窗检测算法
===================

本脚本实现基于YOLOv11的门窗检测功能，支持批量处理图像文件夹中的所有图像。

主要功能：
1. 加载预训练的YOLOv11模型
2. 批量处理输入文件夹中的图像
3. 检测门窗目标并输出边界框坐标
4. 生成可视化结果图像
5. 保存检测结果为JSON格式

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import json
import logging
import argparse
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import time

import cv2
import numpy as np
from PIL import Image
import torch

from ultralytics import YOLO


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('door_windows_detection.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DoorWindowDetector:
    """
    门窗检测器类

    该类封装了YOLOv11模型的加载、推理和结果处理功能，
    专门用于检测图像中的门窗目标。
    """

    def __init__(self, model_path: str, confidence_threshold: float = 0.5):
        """
        初始化门窗检测器

        Args:
            model_path (str): YOLOv11模型文件路径
            confidence_threshold (float): 置信度阈值，默认0.5
        """
        self.model_path = model_path
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.model_type = None  # 'detection' 或 'classification'

        # 根据模型文件名判断模型类型
        if '-cls' in model_path:
            self.model_type = 'classification'
            logger.info("检测到分类模型，将使用ImageNet分类方式")
        else:
            self.model_type = 'detection'
            logger.info("检测到目标检测模型，将使用COCO检测方式")

        # COCO数据集的实际类别 (80个类别，索引0-79) - 用于检测模型
        # 标准COCO数据集中没有专门的门窗类别，但我们可以检测相关物体

        # ImageNet分类模型可能包含的门窗相关类别
        # ImageNet有1000个类别，可能包含门窗相关的类别
        self.imagenet_door_window_keywords = [
            'door', 'window', 'gate', 'entrance', 'exit', 'portal',
            'doorway', 'threshold', 'frame', 'sash', 'shutter',
            'blind', 'curtain', 'drape', 'screen', 'pane',
            'glass', 'glazing', 'casement', 'sliding door',
            'french door', 'bay window', 'skylight'
        ]

        # 由于COCO数据集没有门窗类别，我们需要：
        # 1. 降低置信度阈值以检测更多物体
        # 2. 检测所有类别，然后通过后处理筛选可能的门窗
        # 3. 或者使用专门训练的门窗检测模型

        # 暂时设置为检测所有类别，后续可以根据需要筛选
        self.target_classes = None  # None表示检测所有类别

        # 可能与门窗相关的COCO类别（基于经验判断）
        self.potential_door_window_classes = {
            # 这些类别可能出现在门窗场景中
            'furniture': [56, 57, 58, 59, 60, 61],  # chair, couch, potted plant, bed, dining table, toilet
            'appliances': [72, 73, 74, 75, 76, 77, 78, 79],  # tv, laptop, mouse, remote, keyboard, cell phone, microwave, oven
            'structural': []  # COCO中没有建筑结构类别
        }

        self._load_model()

    def _load_model(self) -> None:
        """
        加载YOLOv11模型

        Raises:
            FileNotFoundError: 当模型文件不存在时抛出
            Exception: 当模型加载失败时抛出
        """
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            logger.info(f"正在加载YOLOv11模型: {self.model_path}")
            self.model = YOLO(self.model_path)
            logger.info("模型加载成功")

            # 打印模型信息
            if hasattr(self.model, 'names'):
                logger.info(f"模型支持的类别: {self.model.names}")

        except Exception as e:
            logger.error(f"模型加载失败: {str(e)}")
            raise

    def detect_image(self, image_path: str) -> Tuple[List[Dict], np.ndarray]:
        """
        对单张图像进行门窗检测

        Args:
            image_path (str): 图像文件路径

        Returns:
            Tuple[List[Dict], np.ndarray]: 检测结果列表和可视化图像

        Raises:
            FileNotFoundError: 当图像文件不存在时抛出
            Exception: 当检测过程出错时抛出
        """
        try:
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图像文件不存在: {image_path}")

            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像文件: {image_path}")

            # 根据模型类型选择不同的处理方式
            if self.model_type == 'classification':
                return self._detect_with_classification_model(image, image_path)
            else:
                return self._detect_with_detection_model(image, image_path)

        except Exception as e:
            logger.error(f"检测图像时出错 {image_path}: {str(e)}")
            raise

    def _detect_with_classification_model(self, image: np.ndarray, image_path: str) -> Tuple[List[Dict], np.ndarray]:
        """
        使用分类模型进行检测

        Args:
            image (np.ndarray): 输入图像
            image_path (str): 图像路径（用于日志）

        Returns:
            Tuple[List[Dict], np.ndarray]: 检测结果列表和可视化图像
        """
        # 进行分类推理
        results = self.model(image)

        detections = []
        annotated_image = image.copy()

        for result in results:
            # 分类模型返回的是概率分布
            if hasattr(result, 'probs') and result.probs is not None:
                probs = result.probs

                # 获取top-k预测结果
                top_k = min(5, len(probs.data))  # 获取前5个最高概率的类别
                top_indices = probs.top5  # 获取top5索引
                top_confidences = probs.top5conf  # 获取top5置信度

                for i in range(top_k):
                    class_id = int(top_indices[i])
                    confidence = float(top_confidences[i])

                    # 只处理置信度高于阈值的结果
                    if confidence < self.confidence_threshold:
                        continue

                    # 获取类别名称
                    class_name = self.model.names[class_id] if hasattr(self.model, 'names') else str(class_id)

                    # 检查是否为门窗相关类别
                    if self._is_door_window_class(class_name, class_id):
                        # 对于分类模型，我们创建一个覆盖整个图像的"检测框"
                        h, w = image.shape[:2]
                        detection = {
                            'class_name': class_name,
                            'class_id': class_id,
                            'confidence': confidence,
                            'bbox': {
                                'x1': 0.0,
                                'y1': 0.0,
                                'x2': float(w),
                                'y2': float(h),
                                'width': float(w),
                                'height': float(h)
                            },
                            'detection_type': 'classification'  # 标记这是分类结果
                        }
                        detections.append(detection)

                        # 在图像上添加分类标签（不绘制边界框，而是添加文本）
                        annotated_image = self._draw_classification_label(
                            annotated_image, class_name, confidence, i
                        )
                    else:
                        # 调试信息：记录所有被过滤掉的分类结果
                        logger.debug(f"过滤掉的分类: {class_name} (ID:{class_id}, 置信度:{confidence:.3f})")

        return detections, annotated_image

    def _detect_with_detection_model(self, image: np.ndarray, image_path: str) -> Tuple[List[Dict], np.ndarray]:
        """
        使用目标检测模型进行检测（原有逻辑）

        Args:
            image (np.ndarray): 输入图像
            image_path (str): 图像路径（用于日志）

        Returns:
            Tuple[List[Dict], np.ndarray]: 检测结果列表和可视化图像
        """
        # 进行推理
        results = self.model(image, conf=self.confidence_threshold)

        # 解析检测结果
        detections = []
        annotated_image = image.copy()

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # 获取边界框坐标
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())

                    # 获取类别名称
                    class_name = self.model.names[class_id] if hasattr(self.model, 'names') else str(class_id)

                    # 临时修改：记录所有检测结果用于调试
                    detection = {
                        'class_name': class_name,
                        'class_id': class_id,
                        'confidence': float(confidence),
                        'bbox': {
                            'x1': float(x1),
                            'y1': float(y1),
                            'x2': float(x2),
                            'y2': float(y2),
                            'width': float(x2 - x1),
                            'height': float(y2 - y1)
                        },
                        'detection_type': 'detection'  # 标记这是检测结果
                    }

                    # 检查是否为目标类别（门窗）
                    if self._is_door_window_class(class_name, class_id):
                        detections.append(detection)

                        # 在图像上绘制边界框
                        annotated_image = self._draw_bbox(
                            annotated_image, x1, y1, x2, y2,
                            class_name, confidence
                        )
                    else:
                        # 调试信息：记录所有被过滤掉的检测结果
                        logger.debug(f"过滤掉的检测: {class_name} (ID:{class_id}, 置信度:{confidence:.3f})")

        return detections, annotated_image

    def _draw_classification_label(self, image: np.ndarray, class_name: str, confidence: float, rank: int) -> np.ndarray:
        """
        在图像上绘制分类标签（用于分类模型结果）

        Args:
            image (np.ndarray): 输入图像
            class_name (str): 类别名称
            confidence (float): 置信度
            rank (int): 排名（用于确定标签位置）

        Returns:
            np.ndarray: 绘制了标签的图像
        """
        # 设置颜色（BGR格式）
        color = (0, 255, 0) if any(kw in class_name.lower() for kw in ['door', 'window', 'gate']) else (255, 0, 0)

        # 准备标签文本
        label = f"#{rank+1}: {class_name} ({confidence:.3f})"

        # 计算文本大小和位置
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        text_thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, text_thickness)

        # 计算标签位置（从上到下排列）
        y_offset = 30 + rank * (text_height + baseline + 10)
        x_offset = 10

        # 绘制文本背景
        cv2.rectangle(image,
                     (x_offset, y_offset - text_height - baseline - 5),
                     (x_offset + text_width + 10, y_offset + 5),
                     color, -1)

        # 绘制文本
        cv2.putText(image, label,
                   (x_offset + 5, y_offset - baseline - 2),
                   font, font_scale, (255, 255, 255), text_thickness)

        return image

    def _is_door_window_class(self, class_name: str, class_id: int) -> bool:
        """
        判断检测到的类别是否为门窗相关类别

        根据模型类型采用不同的判断策略：
        1. 分类模型：检查ImageNet类别中的门窗相关词汇
        2. 检测模型：检查COCO类别中的室内物品
        3. 如果target_classes为None，则接受所有检测结果

        Args:
            class_name (str): 类别名称
            class_id (int): 类别ID

        Returns:
            bool: 如果是目标类别返回True，否则返回False
        """
        # 如果设置为检测所有类别
        if self.target_classes is None:
            # 根据模型类型选择不同的关键词
            if self.model_type == 'classification':
                # ImageNet分类模型：优先检查门窗相关词汇
                door_window_keywords = self.imagenet_door_window_keywords + [
                    # 建筑相关
                    'building', 'house', 'home', 'room', 'wall', 'ceiling', 'floor',
                    # 室内物品
                    'furniture', 'cabinet', 'shelf', 'table', 'chair', 'bed',
                    # 其他可能相关的词汇
                    'opening', 'barrier', 'partition', 'panel'
                ]

                for keyword in door_window_keywords:
                    if keyword.lower() in class_name.lower():
                        return True

                return False
            else:
                # COCO检测模型：检测室内常见物品作为门窗场景的指示
                indoor_keywords = [
                    # 家具类
                    'chair', 'couch', 'bed', 'dining table', 'toilet',
                    # 电器类
                    'tv', 'laptop', 'microwave', 'oven', 'refrigerator', 'sink',
                    # 日用品类
                    'cup', 'bottle', 'cell phone', 'remote', 'clock', 'vase',
                    # 人物
                    'person',
                    # 如果有门窗相关词汇（虽然COCO中没有）
                    'door', 'window', '门', '窗', 'gate', 'entrance'
                ]

                for keyword in indoor_keywords:
                    if keyword.lower() in class_name.lower():
                        return True

                return False

        # 如果有指定的目标类别，则按指定类别筛选
        return class_name.lower() in [cls.lower() for cls in self.target_classes]

    def _draw_bbox(self, image: np.ndarray, x1: float, y1: float, x2: float, y2: float,
                   class_name: str, confidence: float) -> np.ndarray:
        """
        在图像上绘制边界框和标签

        Args:
            image (np.ndarray): 输入图像
            x1, y1, x2, y2 (float): 边界框坐标
            class_name (str): 类别名称
            confidence (float): 置信度

        Returns:
            np.ndarray: 绘制了边界框的图像
        """
        # 设置颜色（BGR格式）
        color = (0, 255, 0) if 'door' in class_name.lower() else (255, 0, 0)  # 门用绿色，窗用蓝色
        thickness = 2

        # 绘制边界框
        cv2.rectangle(image, (int(x1), int(y1)), (int(x2), int(y2)), color, thickness)

        # 准备标签文本
        label = f"{class_name}: {confidence:.2f}"

        # 计算文本大小
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        text_thickness = 1
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, text_thickness)

        # 绘制文本背景
        cv2.rectangle(image,
                     (int(x1), int(y1) - text_height - baseline - 5),
                     (int(x1) + text_width, int(y1)),
                     color, -1)

        # 绘制文本
        cv2.putText(image, label,
                   (int(x1), int(y1) - baseline - 2),
                   font, font_scale, (255, 255, 255), text_thickness)

        return image

    def batch_detect(self, input_dir: str, output_dir: str) -> Dict:
        """
        批量检测文件夹中的所有图像

        Args:
            input_dir (str): 输入图像文件夹路径
            output_dir (str): 输出结果文件夹路径

        Returns:
            Dict: 批量检测的统计结果
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            os.makedirs(os.path.join(output_dir, 'visualizations'), exist_ok=True)

            # 支持的图像格式
            supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

            # 获取所有图像文件
            image_files = []
            for file_path in Path(input_dir).iterdir():
                if file_path.suffix.lower() in supported_formats:
                    image_files.append(file_path)

            if not image_files:
                logger.warning(f"在目录 {input_dir} 中未找到支持的图像文件")
                return {'total_images': 0, 'processed_images': 0, 'total_detections': 0}

            logger.info(f"找到 {len(image_files)} 张图像，开始批量检测...")

            # 批量检测结果
            batch_results = {
                'detection_results': {},
                'statistics': {
                    'total_images': len(image_files),
                    'processed_images': 0,
                    'failed_images': 0,
                    'total_detections': 0,
                    'door_detections': 0,
                    'window_detections': 0
                },
                'processing_time': 0
            }

            start_time = time.time()

            # 逐个处理图像
            for i, image_file in enumerate(image_files, 1):
                try:
                    logger.info(f"处理图像 {i}/{len(image_files)}: {image_file.name}")

                    # 检测当前图像
                    detections, annotated_image = self.detect_image(str(image_file))

                    # 保存检测结果
                    image_result = {
                        'image_path': str(image_file),
                        'image_name': image_file.name,
                        'detections': detections,
                        'detection_count': len(detections)
                    }

                    batch_results['detection_results'][image_file.name] = image_result

                    # 保存可视化结果
                    vis_output_path = os.path.join(output_dir, 'visualizations', f"detected_{image_file.name}")
                    cv2.imwrite(vis_output_path, annotated_image)

                    # 更新统计信息
                    batch_results['statistics']['processed_images'] += 1
                    batch_results['statistics']['total_detections'] += len(detections)

                    # 统计门窗数量
                    for detection in detections:
                        class_name_lower = detection['class_name'].lower()
                        # 检查门相关关键词
                        door_keywords = ['door', 'gate', 'entrance', 'exit', 'portal', 'doorway', 'threshold']
                        # 检查窗相关关键词
                        window_keywords = ['window', 'pane', 'glass', 'glazing', 'casement', 'sash', 'shutter', 'blind', 'curtain', 'skylight']

                        is_door = any(keyword in class_name_lower for keyword in door_keywords)
                        is_window = any(keyword in class_name_lower for keyword in window_keywords)

                        if is_door:
                            batch_results['statistics']['door_detections'] += 1
                        elif is_window:
                            batch_results['statistics']['window_detections'] += 1

                    logger.info(f"图像 {image_file.name} 检测完成，发现 {len(detections)} 个目标")

                except Exception as e:
                    logger.error(f"处理图像 {image_file.name} 时出错: {str(e)}")
                    batch_results['statistics']['failed_images'] += 1
                    continue

            # 计算处理时间
            batch_results['processing_time'] = time.time() - start_time

            # 保存批量检测结果为JSON文件
            results_json_path = os.path.join(output_dir, 'detection_results.json')
            with open(results_json_path, 'w', encoding='utf-8') as f:
                json.dump(batch_results, f, ensure_ascii=False, indent=2)

            # 打印统计信息
            stats = batch_results['statistics']
            logger.info("=" * 50)
            logger.info("批量检测完成！")
            logger.info(f"总图像数: {stats['total_images']}")
            logger.info(f"成功处理: {stats['processed_images']}")
            logger.info(f"处理失败: {stats['failed_images']}")
            logger.info(f"总检测数: {stats['total_detections']}")
            logger.info(f"门检测数: {stats['door_detections']}")
            logger.info(f"窗检测数: {stats['window_detections']}")
            logger.info(f"处理时间: {batch_results['processing_time']:.2f} 秒")
            logger.info(f"结果已保存到: {output_dir}")
            logger.info("=" * 50)

            return batch_results

        except Exception as e:
            logger.error(f"批量检测过程中出错: {str(e)}")
            raise


def create_summary_report(results_json_path: str, output_dir: str) -> None:
    """
    创建检测结果摘要报告

    Args:
        results_json_path (str): 检测结果JSON文件路径
        output_dir (str): 输出目录
    """
    try:
        with open(results_json_path, 'r', encoding='utf-8') as f:
            results = json.load(f)

        # 创建摘要报告
        report_lines = []
        report_lines.append("门窗检测结果摘要报告")
        report_lines.append("=" * 50)
        report_lines.append("")

        # 统计信息
        stats = results['statistics']
        report_lines.append("检测统计:")
        report_lines.append(f"  总图像数: {stats['total_images']}")
        report_lines.append(f"  成功处理: {stats['processed_images']}")
        report_lines.append(f"  处理失败: {stats['failed_images']}")
        report_lines.append(f"  总检测数: {stats['total_detections']}")
        report_lines.append(f"  门检测数: {stats['door_detections']}")
        report_lines.append(f"  窗检测数: {stats['window_detections']}")
        report_lines.append(f"  处理时间: {results['processing_time']:.2f} 秒")
        report_lines.append("")

        # 详细检测结果
        report_lines.append("详细检测结果:")
        report_lines.append("-" * 30)

        for image_name, image_result in results['detection_results'].items():
            report_lines.append(f"\n图像: {image_name}")
            report_lines.append(f"  检测数量: {image_result['detection_count']}")

            if image_result['detections']:
                for i, detection in enumerate(image_result['detections'], 1):
                    bbox = detection['bbox']
                    report_lines.append(f"  检测 {i}:")
                    report_lines.append(f"    类别: {detection['class_name']}")
                    report_lines.append(f"    置信度: {detection['confidence']:.3f}")
                    report_lines.append(f"    边界框: ({bbox['x1']:.1f}, {bbox['y1']:.1f}, {bbox['x2']:.1f}, {bbox['y2']:.1f})")
                    report_lines.append(f"    尺寸: {bbox['width']:.1f} x {bbox['height']:.1f}")
            else:
                report_lines.append("  未检测到门窗目标")

        # 保存报告
        report_path = os.path.join(output_dir, 'detection_summary.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        logger.info(f"摘要报告已保存到: {report_path}")

    except Exception as e:
        logger.error(f"创建摘要报告时出错: {str(e)}")


def main():
    """
    主函数 - 解析命令行参数并执行门窗检测
    """
    parser = argparse.ArgumentParser(
        description='YOLOv11门窗检测算法',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python door_windows_detection.py --input work_dirs/door_windows_imgs --output work_dirs/yolo_v11_results
  python door_windows_detection.py --input /path/to/images --output /path/to/results --model checkpopints/yolo11x.pt --conf 0.6
        """
    )

    parser.add_argument(
        '--input', '-i',
        type=str,
        default='work_dirs/door_windows_imgs',
        help='输入图像文件夹路径 (默认: work_dirs/door_windows_imgs)'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        default='work_dirs/yolo_v11_results',
        help='输出结果文件夹路径 (默认: work_dirs/yolo_v11_results)'
    )

    parser.add_argument(
        '--model', '-m',
        type=str,
        default='checkpopints/yolo11x-cls.pt',
        help='YOLOv11模型文件路径 (默认: checkpopints/yolo11x-cls.pt，分类模型)'
    )

    parser.add_argument(
        '--conf', '-c',
        type=float,
        default=0.5,
        help='置信度阈值 (默认: 0.5)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='显示详细日志信息'
    )

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 验证输入参数
        if not os.path.exists(args.input):
            logger.error(f"输入目录不存在: {args.input}")
            return

        if not os.path.exists(args.model):
            logger.error(f"模型文件不存在: {args.model}")
            return

        if not (0.0 <= args.conf <= 1.0):
            logger.error(f"置信度阈值必须在0.0到1.0之间: {args.conf}")
            return

        # 创建检测器实例
        logger.info("初始化门窗检测器...")
        detector = DoorWindowDetector(
            model_path=args.model,
            confidence_threshold=args.conf
        )

        # 执行批量检测
        logger.info("开始批量检测...")
        batch_results = detector.batch_detect(args.input, args.output)

        # 创建摘要报告
        results_json_path = os.path.join(args.output, 'detection_results.json')
        create_summary_report(results_json_path, args.output)

        # 显示简要统计信息
        stats = batch_results['statistics']
        print(f"\n检测完成! 处理了 {stats['processed_images']}/{stats['total_images']} 张图像")
        print(f"共检测到 {stats['total_detections']} 个目标 (门: {stats['door_detections']}, 窗: {stats['window_detections']})")

        logger.info("门窗检测任务完成！")

    except KeyboardInterrupt:
        logger.info("用户中断了检测过程")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        raise


if __name__ == '__main__':
    main()

