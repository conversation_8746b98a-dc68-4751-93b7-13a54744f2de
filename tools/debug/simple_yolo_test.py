#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的YOLO检测测试脚本
====================

直接使用ultralytics库测试检测功能，不进行任何过滤
"""

import os
import sys
from pathlib import Path

# 尝试导入ultralytics
try:
    from ultralytics import YOLO
    import cv2
    import numpy as np
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请先安装依赖: pip install ultralytics opencv-python")
    sys.exit(1)


def test_yolo_detection():
    """测试YOLO检测功能"""
    print("🔍 简单YOLO检测测试")
    print("=" * 50)
    
    # 模型路径
    model_path = "checkpopints/yolo11x.pt"
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    # 测试图像路径
    input_dir = "work_dirs/door_windows_imgs"
    image_files = list(Path(input_dir).glob("*.jpg"))
    if not image_files:
        print(f"❌ 在目录 {input_dir} 中未找到图像文件")
        return
    
    test_image = image_files[0]  # 使用第一张图像
    print(f"📷 测试图像: {test_image.name}")
    
    try:
        # 加载模型
        print("🔄 加载模型...")
        model = YOLO(model_path)
        
        # 显示模型支持的类别
        if hasattr(model, 'names'):
            print(f"📋 模型支持 {len(model.names)} 个类别")
            print("前20个类别:", list(model.names.values())[:20])
        
        # 进行检测 - 使用很低的置信度阈值
        print(f"\n🔍 检测图像 (置信度阈值: 0.1)...")
        results = model(str(test_image), conf=0.1)
        
        # 显示所有检测结果
        total_detections = 0
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                total_detections = len(boxes)
                print(f"🎯 检测到 {total_detections} 个物体:")
                print("-" * 60)
                print(f"{'ID':>3} {'类别':<15} {'置信度':>8} {'边界框'}")
                print("-" * 60)
                
                for i, box in enumerate(boxes):
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    class_name = model.names[class_id]
                    
                    bbox_str = f"({x1:.0f},{y1:.0f})-({x2:.0f},{y2:.0f})"
                    print(f"{class_id:>3} {class_name:<15} {confidence:>8.3f} {bbox_str}")
        
        if total_detections == 0:
            print("❌ 未检测到任何物体")
            print("\n🔧 可能的原因:")
            print("1. 图像中没有COCO数据集定义的物体")
            print("2. 物体太小或不清晰")
            print("3. 需要进一步降低置信度阈值")
            
            # 尝试更低的置信度
            print(f"\n🔍 尝试更低置信度 (0.01)...")
            results_low = model(str(test_image), conf=0.01)
            for result in results_low:
                boxes = result.boxes
                if boxes is not None:
                    print(f"🎯 低置信度检测到 {len(boxes)} 个物体")
                    break
        
        # 保存检测结果图像
        if total_detections > 0:
            annotated = results[0].plot()
            output_path = "tools/debug/test_detection_output.jpg"
            cv2.imwrite(output_path, annotated)
            print(f"\n💾 检测结果已保存到: {output_path}")
        
    except Exception as e:
        print(f"❌ 检测过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    test_yolo_detection()
