#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11门窗检测调试脚本
======================

本脚本用于测试和调试YOLOv11门窗检测功能，包括：
1. 模型加载测试
2. 单张图像检测测试
3. 检测结果验证
4. 性能测试

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tools.yolov11.door_windows_detection import DoorWindowDetector

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_model_loading():
    """测试模型加载功能"""
    print("=" * 50)
    print("测试1: 模型加载")
    print("=" * 50)
    
    model_path = "checkpopints/yolo11x.pt"
    
    try:
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return False
        
        print(f"📁 模型路径: {model_path}")
        print("🔄 正在加载模型...")
        
        start_time = time.time()
        detector = DoorWindowDetector(model_path, confidence_threshold=0.5)
        load_time = time.time() - start_time
        
        print(f"✅ 模型加载成功! 耗时: {load_time:.2f} 秒")
        
        # 打印模型信息
        if hasattr(detector.model, 'names'):
            print(f"📋 模型支持的类别数: {len(detector.model.names)}")
            print(f"📋 前10个类别: {list(detector.model.names.values())[:10]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        return False


def test_single_image_detection():
    """测试单张图像检测"""
    print("\n" + "=" * 50)
    print("测试2: 单张图像检测")
    print("=" * 50)
    
    model_path = "checkpopints/yolo11x.pt"
    input_dir = "work_dirs/door_windows_imgs"
    
    try:
        # 获取第一张测试图像
        image_files = list(Path(input_dir).glob("*.jpg"))
        if not image_files:
            print(f"❌ 在目录 {input_dir} 中未找到图像文件")
            return False
        
        test_image = image_files[0]
        print(f"📷 测试图像: {test_image.name}")
        
        # 创建检测器
        detector = DoorWindowDetector(model_path, confidence_threshold=0.3)
        
        # 执行检测
        print("🔍 正在执行检测...")
        start_time = time.time()
        detections, annotated_image = detector.detect_image(str(test_image))
        detection_time = time.time() - start_time
        
        print(f"⏱️  检测耗时: {detection_time:.3f} 秒")
        print(f"🎯 检测到 {len(detections)} 个目标")
        
        # 显示检测结果
        if detections:
            for i, detection in enumerate(detections, 1):
                bbox = detection['bbox']
                print(f"  目标 {i}:")
                print(f"    类别: {detection['class_name']}")
                print(f"    置信度: {detection['confidence']:.3f}")
                print(f"    边界框: ({bbox['x1']:.1f}, {bbox['y1']:.1f}) -> ({bbox['x2']:.1f}, {bbox['y2']:.1f})")
                print(f"    尺寸: {bbox['width']:.1f} x {bbox['height']:.1f}")
        else:
            print("  未检测到门窗目标")
        
        # 保存测试结果
        import cv2
        output_path = "tools/debug/test_detection_result.jpg"
        cv2.imwrite(output_path, annotated_image)
        print(f"💾 检测结果已保存到: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单张图像检测失败: {str(e)}")
        return False


def test_batch_processing_sample():
    """测试小批量处理"""
    print("\n" + "=" * 50)
    print("测试3: 小批量处理测试")
    print("=" * 50)
    
    model_path = "checkpopints/yolo11x.pt"
    input_dir = "work_dirs/door_windows_imgs"
    output_dir = "tools/debug/test_output"
    
    try:
        # 获取前5张图像进行测试
        image_files = list(Path(input_dir).glob("*.jpg"))[:5]
        if not image_files:
            print(f"❌ 在目录 {input_dir} 中未找到图像文件")
            return False
        
        print(f"📁 测试图像数量: {len(image_files)}")
        
        # 创建临时测试目录
        test_input_dir = "tools/debug/test_input"
        os.makedirs(test_input_dir, exist_ok=True)
        
        # 复制测试图像
        import shutil
        for img_file in image_files:
            shutil.copy2(img_file, test_input_dir)
        
        # 创建检测器并执行批量检测
        detector = DoorWindowDetector(model_path, confidence_threshold=0.3)
        
        print("🔄 开始批量检测...")
        start_time = time.time()
        results = detector.batch_detect(test_input_dir, output_dir)
        total_time = time.time() - start_time
        
        # 显示结果统计
        stats = results['statistics']
        print(f"✅ 批量检测完成!")
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📊 处理统计:")
        print(f"  - 总图像数: {stats['total_images']}")
        print(f"  - 成功处理: {stats['processed_images']}")
        print(f"  - 处理失败: {stats['failed_images']}")
        print(f"  - 总检测数: {stats['total_detections']}")
        print(f"  - 平均每张图像检测时间: {total_time/stats['processed_images']:.3f} 秒")
        
        # 清理临时文件
        shutil.rmtree(test_input_dir, ignore_errors=True)
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {str(e)}")
        return False


def test_performance_benchmark():
    """性能基准测试"""
    print("\n" + "=" * 50)
    print("测试4: 性能基准测试")
    print("=" * 50)
    
    model_path = "checkpopints/yolo11x.pt"
    input_dir = "work_dirs/door_windows_imgs"
    
    try:
        # 获取测试图像
        image_files = list(Path(input_dir).glob("*.jpg"))[:10]  # 测试前10张
        if not image_files:
            print(f"❌ 在目录 {input_dir} 中未找到图像文件")
            return False
        
        print(f"🏃 性能测试 - 使用 {len(image_files)} 张图像")
        
        # 创建检测器
        detector = DoorWindowDetector(model_path, confidence_threshold=0.5)
        
        # 预热
        print("🔥 模型预热中...")
        detector.detect_image(str(image_files[0]))
        
        # 性能测试
        print("⏱️  开始性能测试...")
        detection_times = []
        total_detections = 0
        
        for i, image_file in enumerate(image_files, 1):
            start_time = time.time()
            detections, _ = detector.detect_image(str(image_file))
            detection_time = time.time() - start_time
            
            detection_times.append(detection_time)
            total_detections += len(detections)
            
            print(f"  图像 {i}: {detection_time:.3f}s, 检测数: {len(detections)}")
        
        # 计算统计信息
        avg_time = sum(detection_times) / len(detection_times)
        min_time = min(detection_times)
        max_time = max(detection_times)
        
        print(f"\n📈 性能统计:")
        print(f"  - 平均检测时间: {avg_time:.3f} 秒")
        print(f"  - 最快检测时间: {min_time:.3f} 秒")
        print(f"  - 最慢检测时间: {max_time:.3f} 秒")
        print(f"  - 总检测目标数: {total_detections}")
        print(f"  - 平均每张图像检测数: {total_detections/len(image_files):.1f}")
        print(f"  - 检测吞吐量: {len(image_files)/sum(detection_times):.1f} 图像/秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 YOLOv11门窗检测调试测试开始")
    print("=" * 60)
    
    # 创建调试输出目录
    os.makedirs("tools/debug", exist_ok=True)
    
    # 执行所有测试
    tests = [
        ("模型加载测试", test_model_loading),
        ("单张图像检测测试", test_single_image_detection),
        ("小批量处理测试", test_batch_processing_sample),
        ("性能基准测试", test_performance_benchmark)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("🏁 测试总结")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("🎉 所有测试通过! YOLOv11门窗检测功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关配置和依赖")


if __name__ == '__main__':
    main()
