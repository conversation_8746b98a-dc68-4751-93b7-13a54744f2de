#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11分类模型测试脚本
======================

本脚本用于测试YOLOv11分类模型(yolo11x-cls.pt)的门窗检测功能

功能：
1. 测试分类模型加载
2. 检查ImageNet类别中是否包含门窗相关类别
3. 对测试图像进行分类
4. 比较分类模型和检测模型的结果

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from ultralytics import YOLO
    import cv2
    import numpy as np
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请先安装依赖: pip install ultralytics opencv-python")
    sys.exit(1)


def test_classification_model():
    """测试分类模型功能"""
    print("🔍 测试YOLOv11分类模型")
    print("=" * 50)
    
    # 模型路径
    cls_model_path = "checkpopints/yolo11x-cls.pt"
    det_model_path = "checkpopints/yolo11x.pt"
    
    if not os.path.exists(cls_model_path):
        print(f"❌ 分类模型文件不存在: {cls_model_path}")
        return
    
    if not os.path.exists(det_model_path):
        print(f"❌ 检测模型文件不存在: {det_model_path}")
        return
    
    try:
        # 加载分类模型
        print("🔄 加载分类模型...")
        cls_model = YOLO(cls_model_path)
        
        # 加载检测模型用于对比
        print("🔄 加载检测模型...")
        det_model = YOLO(det_model_path)
        
        # 显示模型信息
        print(f"\n📋 分类模型信息:")
        if hasattr(cls_model, 'names'):
            print(f"  支持类别数: {len(cls_model.names)}")
            
            # 查找门窗相关类别
            door_window_classes = []
            door_window_keywords = [
                'door', 'window', 'gate', 'entrance', 'exit', 'portal',
                'doorway', 'threshold', 'frame', 'sash', 'shutter',
                'blind', 'curtain', 'drape', 'screen', 'pane',
                'glass', 'glazing', 'casement', 'sliding', 'french',
                'bay', 'skylight', 'building', 'house', 'home', 'room'
            ]
            
            for class_id, class_name in cls_model.names.items():
                for keyword in door_window_keywords:
                    if keyword.lower() in class_name.lower():
                        door_window_classes.append((class_id, class_name))
                        break
            
            print(f"  门窗相关类别数: {len(door_window_classes)}")
            if door_window_classes:
                print("  门窗相关类别:")
                for class_id, class_name in door_window_classes[:10]:  # 显示前10个
                    print(f"    {class_id:4d}: {class_name}")
                if len(door_window_classes) > 10:
                    print(f"    ... 还有 {len(door_window_classes) - 10} 个类别")
            else:
                print("  ⚠️  未找到明显的门窗相关类别")
        
        print(f"\n📋 检测模型信息:")
        if hasattr(det_model, 'names'):
            print(f"  支持类别数: {len(det_model.names)}")
            print(f"  前10个类别: {list(det_model.names.values())[:10]}")
        
        # 测试图像分类
        print(f"\n🧪 测试图像分类:")
        test_images = list(Path("work_dirs/door_windows_imgs").glob("*.jpg"))[:3]
        
        if not test_images:
            print("❌ 未找到测试图像")
            return
        
        for i, image_path in enumerate(test_images, 1):
            print(f"\n--- 测试图像 {i}: {image_path.name} ---")
            
            # 读取图像
            image = cv2.imread(str(image_path))
            if image is None:
                print(f"❌ 无法读取图像: {image_path}")
                continue
            
            print(f"📷 图像尺寸: {image.shape[1]} x {image.shape[0]}")
            
            # 分类模型推理
            print("🔍 分类模型结果:")
            cls_results = cls_model(image)
            
            for result in cls_results:
                if hasattr(result, 'probs') and result.probs is not None:
                    probs = result.probs
                    top_k = min(5, len(probs.data))
                    
                    print(f"  Top-{top_k} 分类结果:")
                    for j in range(top_k):
                        class_id = int(probs.top5[j])
                        confidence = float(probs.top5conf[j])
                        class_name = cls_model.names[class_id]
                        
                        # 检查是否为门窗相关
                        is_door_window = any(kw in class_name.lower() for kw in door_window_keywords)
                        marker = "🚪🪟" if is_door_window else "  "
                        
                        print(f"    {marker} #{j+1}: {class_name} ({confidence:.3f})")
            
            # 检测模型推理（用于对比）
            print("🔍 检测模型结果:")
            det_results = det_model(image, conf=0.1)
            
            detection_count = 0
            for result in det_results:
                if result.boxes is not None:
                    detection_count = len(result.boxes)
                    print(f"  检测到 {detection_count} 个目标")
                    
                    if detection_count > 0:
                        print("  检测类别:")
                        for box in result.boxes[:5]:  # 显示前5个
                            class_id = int(box.cls[0])
                            confidence = float(box.conf[0])
                            class_name = det_model.names[class_id]
                            print(f"    - {class_name} ({confidence:.3f})")
                        
                        if detection_count > 5:
                            print(f"    ... 还有 {detection_count - 5} 个检测结果")
            
            if detection_count == 0:
                print("  未检测到任何目标")
        
        print(f"\n✅ 分类模型测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()


def test_door_window_detection_with_classification():
    """测试使用分类模型进行门窗检测"""
    print(f"\n" + "=" * 60)
    print("🚪🪟 测试分类模型门窗检测功能")
    print("=" * 60)
    
    try:
        # 导入我们的检测器
        from tools.yolov11.door_windows_detection import DoorWindowDetector
        
        # 创建分类模型检测器
        print("🔄 创建分类模型检测器...")
        cls_detector = DoorWindowDetector(
            model_path="checkpopints/yolo11x-cls.pt",
            confidence_threshold=0.1
        )
        
        # 测试单张图像
        test_images = list(Path("work_dirs/door_windows_imgs").glob("*.jpg"))[:2]
        
        for i, image_path in enumerate(test_images, 1):
            print(f"\n--- 分类检测测试 {i}: {image_path.name} ---")
            
            detections, annotated_image = cls_detector.detect_image(str(image_path))
            
            print(f"🎯 检测结果: {len(detections)} 个目标")
            
            if detections:
                for j, detection in enumerate(detections, 1):
                    print(f"  目标 {j}:")
                    print(f"    类别: {detection['class_name']}")
                    print(f"    置信度: {detection['confidence']:.3f}")
                    print(f"    类型: {detection.get('detection_type', 'unknown')}")
            else:
                print("  未检测到门窗相关目标")
            
            # 保存结果图像
            output_path = f"tools/debug/cls_test_result_{i}.jpg"
            cv2.imwrite(output_path, annotated_image)
            print(f"💾 结果已保存到: {output_path}")
        
        print(f"\n✅ 分类检测测试完成!")
        
    except Exception as e:
        print(f"❌ 分类检测测试出错: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 YOLOv11分类模型门窗检测测试")
    print("=" * 60)
    
    # 创建调试输出目录
    os.makedirs("tools/debug", exist_ok=True)
    
    # 测试1: 基本分类模型功能
    test_classification_model()
    
    # 测试2: 门窗检测功能
    test_door_window_detection_with_classification()
    
    print(f"\n🏁 所有测试完成!")


if __name__ == '__main__':
    main()
