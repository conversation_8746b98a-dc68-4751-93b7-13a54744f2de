#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv11类别检查脚本
==================

本脚本用于检查YOLOv11模型实际能检测到的类别，
帮助调试门窗检测问题。

功能：
1. 加载YOLOv11模型并显示所有支持的类别
2. 对测试图像进行检测，显示所有检测结果
3. 分析哪些类别被检测到
4. 提供门窗检测的建议

作者: AI Assistant
创建时间: 2025-09-01
"""

import os
import sys
from pathlib import Path
import cv2
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from ultralytics import YOLO


def check_model_classes():
    """检查模型支持的所有类别"""
    print("🔍 检查YOLOv11模型支持的类别")
    print("=" * 50)
    
    model_path = "checkpopints/yolo11x.pt"
    
    try:
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return None
        
        # 加载模型
        model = YOLO(model_path)
        
        # 显示模型信息
        if hasattr(model, 'names'):
            print(f"📋 模型支持 {len(model.names)} 个类别:")
            print("-" * 30)
            
            for class_id, class_name in model.names.items():
                print(f"  {class_id:2d}: {class_name}")
            
            print("-" * 30)
            
            # 查找可能与门窗相关的类别
            door_window_related = []
            keywords = ['door', 'window', 'chair', 'couch', 'bed', 'table', 'tv', 'person', 'bottle', 'cup']
            
            for class_id, class_name in model.names.items():
                for keyword in keywords:
                    if keyword.lower() in class_name.lower():
                        door_window_related.append((class_id, class_name))
                        break
            
            if door_window_related:
                print(f"\n🎯 可能相关的类别 ({len(door_window_related)} 个):")
                for class_id, class_name in door_window_related:
                    print(f"  {class_id:2d}: {class_name}")
            
            return model
        else:
            print("❌ 无法获取模型类别信息")
            return None
            
    except Exception as e:
        print(f"❌ 检查模型类别时出错: {e}")
        return None


def detect_all_objects_in_image(model, image_path, confidence_threshold=0.3):
    """检测图像中的所有物体（不进行类别筛选）"""
    print(f"\n🔍 检测图像中的所有物体: {Path(image_path).name}")
    print("=" * 50)
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return
        
        print(f"📷 图像尺寸: {image.shape[1]} x {image.shape[0]}")
        
        # 进行检测
        results = model(image, conf=confidence_threshold)
        
        all_detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # 获取检测信息
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    class_name = model.names[class_id]
                    
                    detection = {
                        'class_id': class_id,
                        'class_name': class_name,
                        'confidence': float(confidence),
                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                        'area': float((x2-x1) * (y2-y1))
                    }
                    all_detections.append(detection)
        
        # 按置信度排序
        all_detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        print(f"🎯 检测到 {len(all_detections)} 个物体:")
        print("-" * 60)
        
        if all_detections:
            print(f"{'ID':>3} {'类别':<15} {'置信度':>6} {'面积':>8} {'边界框'}")
            print("-" * 60)
            
            for detection in all_detections:
                bbox_str = f"({detection['bbox'][0]:.0f},{detection['bbox'][1]:.0f})-({detection['bbox'][2]:.0f},{detection['bbox'][3]:.0f})"
                print(f"{detection['class_id']:>3} {detection['class_name']:<15} {detection['confidence']:>6.3f} {detection['area']:>8.0f} {bbox_str}")
        else:
            print("❌ 未检测到任何物体")
            print(f"   建议降低置信度阈值 (当前: {confidence_threshold})")
        
        return all_detections
        
    except Exception as e:
        print(f"❌ 检测过程出错: {e}")
        return []


def analyze_detection_results(detections):
    """分析检测结果并提供建议"""
    print(f"\n📊 检测结果分析")
    print("=" * 50)
    
    if not detections:
        print("❌ 没有检测结果可分析")
        print("\n💡 建议:")
        print("1. 降低置信度阈值 (尝试 0.1-0.3)")
        print("2. 检查图像是否包含清晰的物体")
        print("3. 考虑使用专门训练的门窗检测模型")
        return
    
    # 统计类别
    class_counts = {}
    for detection in detections:
        class_name = detection['class_name']
        if class_name not in class_counts:
            class_counts[class_name] = 0
        class_counts[class_name] += 1
    
    print(f"📈 检测到的类别统计:")
    for class_name, count in sorted(class_counts.items()):
        print(f"  {class_name}: {count} 个")
    
    # 置信度分析
    confidences = [d['confidence'] for d in detections]
    print(f"\n📊 置信度分析:")
    print(f"  最高置信度: {max(confidences):.3f}")
    print(f"  最低置信度: {min(confidences):.3f}")
    print(f"  平均置信度: {sum(confidences)/len(confidences):.3f}")
    
    # 门窗检测建议
    print(f"\n💡 门窗检测建议:")
    
    # 检查是否有室内物品
    indoor_items = ['chair', 'couch', 'bed', 'dining table', 'tv', 'laptop', 'person']
    found_indoor = [item for item in indoor_items if any(item in d['class_name'] for d in detections)]
    
    if found_indoor:
        print(f"✅ 检测到室内物品: {', '.join(found_indoor)}")
        print("   这表明图像可能包含室内场景，但COCO模型无法直接检测门窗")
    
    print("\n🔧 解决方案:")
    print("1. 使用专门的门窗检测模型")
    print("2. 基于检测到的室内物品推断门窗位置")
    print("3. 使用图像分割方法检测建筑结构")
    print("4. 训练自定义的门窗检测模型")


def main():
    """主函数"""
    print("🔍 YOLOv11类别检查和调试工具")
    print("=" * 60)
    
    # 1. 检查模型类别
    model = check_model_classes()
    if model is None:
        return
    
    # 2. 选择测试图像
    input_dir = "work_dirs/door_windows_imgs"
    image_files = list(Path(input_dir).glob("*.jpg"))
    
    if not image_files:
        print(f"\n❌ 在目录 {input_dir} 中未找到图像文件")
        return
    
    # 测试前3张图像
    test_images = image_files[:3]
    print(f"\n🧪 将测试 {len(test_images)} 张图像")
    
    all_results = []
    for i, image_file in enumerate(test_images, 1):
        print(f"\n{'='*20} 测试图像 {i}/{len(test_images)} {'='*20}")
        detections = detect_all_objects_in_image(model, str(image_file), confidence_threshold=0.2)
        all_results.extend(detections)
    
    # 3. 分析所有结果
    print(f"\n{'='*20} 总体分析 {'='*20}")
    analyze_detection_results(all_results)
    
    print(f"\n🏁 检查完成!")
    print("请根据上述分析结果调整门窗检测策略")


if __name__ == '__main__':
    main()
