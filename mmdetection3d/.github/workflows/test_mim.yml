name: test-mim

on:
  push:
    paths:
      - 'model-index.yml'
      - 'configs/**'

  pull_request:
    paths:
      - 'model-index.yml'
      - 'configs/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build_cpu:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: [3.7]
        torch: [1.8.1]
        include:
          - torch: 1.8.1
            torch_version: torch1.8
            torchvision: 0.9.1
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Upgrade pip
        run: pip install pip --upgrade && pip install wheel
      - name: Install PyTorch
        run: pip install torch==${{matrix.torch}}+cpu torchvision==${{matrix.torchvision}}+cpu -f https://download.pytorch.org/whl/cpu/torch_stable.html
      - name: Install openmim
        run: pip install openmim
      - name: Build and install
        run: rm -rf .eggs && mim install -e .
      - name: test commands of mim
        run: mim search mmdet3d
