version: 2.1

# the default pipeline parameters, which will be updated according to
# the results of the path-filtering orb
parameters:
  lint_only:
    type: boolean
    default: true

jobs:
  lint:
    docker:
      - image: cimg/python:3.7.4
    steps:
      - checkout
      - run:
          name: Install pre-commit hook
          command: |
            pip install pre-commit
            pre-commit install
      - run:
          name: Linting
          command: pre-commit run --all-files
      - run:
          name: Check docstring coverage
          command: |
            pip install interrogate
            interrogate -v --ignore-init-method --ignore-module --ignore-nested-functions --ignore-magic --ignore-regex "__repr__" --fail-under 90 mmdet3d

  build_cpu:
    parameters:
      # The python version must match available image tags in
      # https://circleci.com/developer/images/image/cimg/python
      python:
        type: string
      torch:
        type: string
      torchvision:
        type: string
    docker:
      - image: cimg/python:<< parameters.python >>
    resource_class: large
    steps:
      - checkout
      - run:
          name: Install Libraries
          command: |
            sudo apt-get update
            sudo apt-get install -y ninja-build libglib2.0-0 libsm6 libxrender-dev libxext6 libgl1-mesa-glx libjpeg-dev zlib1g-dev libtinfo-dev libncurses5
      - run:
          name: Configure Python & pip
          command: |
            pip install --upgrade pip
            pip install wheel
      - run:
          name: Install PyTorch
          command: pip install torch==<< parameters.torch >>+cpu torchvision==<< parameters.torchvision >>+cpu -f https://download.pytorch.org/whl/torch_stable.html
      - when:
          condition:
            equal: ["3.9.0", << parameters.python >>]
          steps:
            - run: pip install "protobuf <= 3.20.1" && sudo apt-get update && sudo apt-get -y install libprotobuf-dev protobuf-compiler cmake
      - run:
          name: Install mmdet3d dependencies
          command: |
            pip install git+ssh://**************/open-mmlab/mmengine.git@main
            pip install -U openmim
            mim install 'mmcv >= 2.0.0rc4'
            pip install git+ssh://**************/open-mmlab/mmdetection.git@dev-3.x
            pip install -r requirements/tests.txt
      - run:
          name: Build and install
          command: |
            pip install -e .
      - run:
          name: Run unittests
          command: |
            coverage run --branch --source mmdet3d -m pytest tests/
            coverage xml
            coverage report -m

  build_cuda:
    parameters:
      torch:
        type: string
      cuda:
        type: enum
        enum: ["10.2", "11.7"]
      cudnn:
        type: integer
        default: 8
    machine:
      image: linux-cuda-11:default
      # docker_layer_caching: true
    resource_class: gpu.nvidia.small.multi
    steps:
      - checkout
      - run:
          name: Install nvidia-container-toolkit and Restart Docker
          command: |
            sudo apt-get update
            sudo apt-get install -y nvidia-container-toolkit
            sudo systemctl restart docker
      - run:
          # Cloning repos in VM since Docker doesn't have access to the private key
          name: Clone Repos
          command: |
            git clone -b main --depth 1 ssh://**************/open-mmlab/mmengine.git /home/<USER>/mmengine
            git clone -b dev-3.x --depth 1 ssh://**************/open-mmlab/mmdetection.git /home/<USER>/mmdetection
      - run:
          name: Build Docker image
          command: |
            docker build .circleci/docker -t mmdet3d:gpu --build-arg PYTORCH=<< parameters.torch >> --build-arg CUDA=<< parameters.cuda >> --build-arg CUDNN=<< parameters.cudnn >>
            docker run --gpus all -t -d -v /home/<USER>/project:/mmdetection3d -v /home/<USER>/mmengine:/mmengine -v /home/<USER>/mmdetection:/mmdetection -w /mmdetection3d --name mmdet3d mmdet3d:gpu
            docker exec mmdet3d apt-get install -y git
      - run:
          name: Install mmdet3d dependencies
          command: |
            docker exec mmdet3d pip install -e /mmengine
            docker exec mmdet3d pip install -U openmim
            docker exec mmdet3d mim install 'mmcv >= 2.0.0rc4'
            docker exec mmdet3d pip install -e /mmdetection
            docker exec mmdet3d pip install -r requirements/tests.txt
      - run:
          name: Build and install
          command: |
            docker exec mmdet3d pip install -e .
      - run:
          name: Run unittests
          command: |
            docker exec mmdet3d pytest tests/

workflows:
  pr_stage_lint:
    when: << pipeline.parameters.lint_only >>
    jobs:
      - lint:
          name: lint
          filters:
            branches:
              ignore:
                - dev-1.x
  pr_stage_test:
    when:
      not: << pipeline.parameters.lint_only >>
    jobs:
      - lint:
          name: lint
          filters:
            branches:
              ignore:
                - dev-1.x
      - build_cpu:
          name: minimum_version_cpu
          torch: 1.8.1
          torchvision: 0.9.1
          python: 3.7.4 # The lowest python 3.7.x version available on CircleCI images
          requires:
            - lint
      - build_cpu:
          name: maximum_version_cpu
          torch: 2.0.0
          torchvision: 0.15.1
          python: 3.9.0
          requires:
            - minimum_version_cpu
      - hold:
          type: approval
          requires:
            - maximum_version_cpu
      - build_cuda:
          name: mainstream_version_gpu
          torch: 1.8.1
          # Use double quotation mark to explicitly specify its type
          # as string instead of number
          cuda: "10.2"
          cudnn: 7
          requires:
            - hold
      - build_cuda:
          name: maximum_version_gpu
          torch: 2.0.0
          cuda: "11.7"
          cudnn: 8
          requires:
            - hold
  merge_stage_test:
    when:
      not: << pipeline.parameters.lint_only >>
    jobs:
      - build_cuda:
          name: minimum_version_gpu
          torch: 1.8.1
          cuda: "10.2"
          cudnn: 7
          filters:
            branches:
              only:
                - dev-1.x
