Collections:
  - Name: Part-A^2
    Metadata:
      Training Data: KITTI
      Training Techniques:
        - AdamW
      Training Resources: 8x V100 GPUs
      Architecture:
        - Sparse U-Net
    Paper:
      URL: https://arxiv.org/abs/1907.03670
      Title: 'From Points to Parts: 3D Object Detection from Point Cloud with Part-aware and Part-aggregation Network'
    README: configs/parta2/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/detectors/parta2.py#L12
      Version: v0.5.0

Models:
  - Name: parta2_hv_secfpn_8xb2-cyclic-80e_kitti-3d-3class
    In Collection: Part-A^2
    Config: configs/parta2/parta2_hv_secfpn_8xb2-cyclic-80e_kitti-3d-3class.py
    Metadata:
      Training Memory (GB): 4.1
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 68.33
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/parta2/hv_PartA2_secfpn_2x8_cyclic_80e_kitti-3d-3class/hv_PartA2_secfpn_2x8_cyclic_80e_kitti-3d-3class_20210831_022017-454a5344.pth

  - Name: parta2_hv_secfpn_8xb2-cyclic-80e_kitti-3d-car
    In Collection: Part-A^2
    Config: configs/parta2/parta2_hv_secfpn_8xb2-cyclic-80e_kitti-3d-car.py
    Metadata:
      Training Memory (GB): 4.0
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 79.08
    Weights: https://download.openmmlab.com/mmdetection3d/v1.0.0_models/parta2/hv_PartA2_secfpn_2x8_cyclic_80e_kitti-3d-car/hv_PartA2_secfpn_2x8_cyclic_80e_kitti-3d-car_20210831_022017-cb7ff621.pth
