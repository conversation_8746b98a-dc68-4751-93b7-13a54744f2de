Collections:
  - Name: SECOND
    Metadata:
      Training Techniques:
        - AdamW
      Architecture:
        - Hard Voxelization
    Paper:
      URL: https://www.mdpi.com/1424-8220/18/10/3337
      Title: 'SECOND: Sparsely Embedded Convolutional Detection'
    README: configs/second/README.md
    Code:
      URL: https://github.com/open-mmlab/mmdetection3d/blob/master/mmdet3d/models/backbones/second.py#L11
      Version: v0.5.0

Models:
  - Name: second_hv_secfpn_8xb6-80e_kitti-3d-car
    In Collection: SECOND
    Config: configs/second/second_hv_secfpn_8xb6-80e_kitti-3d-car.py
    Metadata:
      Training Data: KITTI
      Training Memory (GB): 5.4
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 78.2
    Weights: https://download.openmmlab.com/mmdetection3d/v1.1.0_models/second/second_hv_secfpn_8xb6-80e_kitti-3d-car/second_hv_secfpn_8xb6-80e_kitti-3d-car-75d9305e.pth

  - Name: second_hv_secfpn_8xb6-80e_kitti-3d-3class
    In Collection: SECOND
    Config: configs/second/second_hv_secfpn_8xb6-80e_kitti-3d-3class.py
    Metadata:
      Training Data: KITTI
      Training Memory (GB): 5.4
      Training Resources: 8x V100 GPUs
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 65.3
    Weights: https://download.openmmlab.com/mmdetection3d/v1.1.0_models/second/second_hv_secfpn_8xb6-80e_kitti-3d-3class/second_hv_secfpn_8xb6-80e_kitti-3d-3class-b086d0a3.pth

  - Name: second_hv_secfpn_sbn-all_16xb2-2x_waymoD5-3d-3class
    In Collection: SECOND
    Config: configs/second/second_hv_secfpn_sbn-all_16xb2-2x_waymoD5-3d-3class.py
    Metadata:
      Training Data: Waymo
      Training Memory (GB): 8.12
      Training Resources: 8x GeForce GTX 1080 Ti
    Results:
      - Task: 3D Object Detection
        Dataset: Waymo
        Metrics:
          mAP@L1: 65.3
          mAPH@L1: 61.7
          mAP@L2: 58.9
          mAPH@L2: 55.7

  - Name: second_hv_secfpn_8xb6-amp-80e_kitti-3d-car
    In Collection: SECOND
    Config: configs/second/second_hv_secfpn_8xb6-amp-80e_kitti-3d-car.py
    Metadata:
      Training Techniques:
        - AdamW
        - Mixed Precision Training
      Training Resources: 8x TITAN Xp
      Training Data: KITTI
      Training Memory (GB): 2.9
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 78.72
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/fp16/hv_second_secfpn_fp16_6x8_80e_kitti-3d-car/hv_second_secfpn_fp16_6x8_80e_kitti-3d-car_20200924_211301-1f5ad833.pth
    Code:
      Version: v0.7.0

  - Name: second_hv_secfpn_8xb6-amp-80e_kitti-3d-3class
    In Collection: SECOND
    Config: configs/second/second_hv_secfpn_8xb6-amp-80e_kitti-3d-3class.py
    Metadata:
      Training Techniques:
        - AdamW
        - Mixed Precision Training
      Training Resources: 8x TITAN Xp
      Training Data: KITTI
      Training Memory (GB): 2.9
    Results:
      - Task: 3D Object Detection
        Dataset: KITTI
        Metrics:
          mAP: 67.4
    Weights: https://download.openmmlab.com/mmdetection3d/v0.1.0_models/fp16/hv_second_secfpn_fp16_6x8_80e_kitti-3d-3class/hv_second_secfpn_fp16_6x8_80e_kitti-3d-3class_20200925_110059-05f67bdf.pth
    Code:
      Version: v0.7.0
